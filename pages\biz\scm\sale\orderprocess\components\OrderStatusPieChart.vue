<template>
	<view class="pie-chart-container">
		<view class="chart-header">
			<view class="chart-title">订单状态分布</view>
		</view>
		<view class="chart-content">
			<l-echart ref="chartRef" @finished="initChart"></l-echart>
		</view>
	</view>
</template>

<script>
// 引入echarts - 根据uni_modules的lime-echart文档，项目使用Vue2
import * as echarts from '@/uni_modules/lime-echart/static/echarts.min'

export default {
	name: 'OrderStatusPieChart',
	props: {
		statistics: {
			type: Object,
			default: () => ({})
		}
	},
	data() {
		return {
			// chart实例不放在data中，避免Vue响应式包装
		}
	},

	// 将chart实例放在组件实例上，避免Vue响应式包装
	created() {
		this.chart = null
	},
	watch: {
		statistics: {
			handler(newVal, oldVal) {
				// 只有在数据真正变化时才更新
				if (newVal && this.chart) {
					this.$nextTick(() => {
						this.updateChart()
					})
				}
			},
			deep: true
		}
	},
	methods: {
		// 获取履约状态饼图数据
		getPieData() {
			const stats = this.statistics || {}
			return [
				{
					name: '履约完成',
					value: stats.completedOrders || 25,
					itemStyle: { color: '#10b981' }
				},
				{
					name: '履约中',
					value: stats.processingOrders || 18,
					itemStyle: { color: '#2563eb' }
				},
				{
					name: '逾期异常',
					value: stats.overdueOrders || 8,
					itemStyle: { color: '#ef4444' }
				},
				{
					name: '待履约',
					value: Math.max(0, (stats.totalOrders || 65) - (stats.completedOrders || 25) - (stats.processingOrders || 18) - (stats.overdueOrders || 8)),
					itemStyle: { color: '#f59e0b' }
				}
			].filter(item => item.value > 0) // 过滤掉值为0的项
		},

		// 初始化图表
		initChart() {
			if (!this.$refs.chartRef) return

			try {
				// 根据lime-echart文档，Vue2项目中的初始化方式
				this.$refs.chartRef.init(echarts, (chart) => {
					// 使用Object.defineProperty避免Vue响应式包装
					Object.defineProperty(this, 'chart', {
						value: chart,
						writable: true,
						enumerable: false,
						configurable: true
					})

					// 延迟更新图表，确保DOM完全渲染
					this.$nextTick(() => {
						this.updateChart()
					})
				})
			} catch (error) {
				console.error('初始化饼图失败:', error)
			}
		},

		// 更新图表
		updateChart() {
			if (!this.chart || !this.$refs.chartRef) return

			try {
				const pieData = this.getPieData()

			const option = {
				tooltip: {
					trigger: 'item',
					formatter: '{a} <br/>{b}: {c} ({d}%)'
				},
				legend: {
					top: 10,
					right: 10,
					orient: 'vertical',
					textStyle: {
						fontSize: 10
					},
					itemWidth: 8,
					itemHeight: 8,
					itemGap: 8
				},
				series: [{
					name: '履约状态',
					type: 'pie',
					radius: ['40%', '70%'],
					center: ['40%', '50%'],
					data: pieData,
					emphasis: {
						itemStyle: {
							shadowBlur: 10,
							shadowOffsetX: 0,
							shadowColor: 'rgba(0, 0, 0, 0.5)'
						}
					},
					label: {
						show: false
					},
					labelLine: {
						show: false
					}
				}]
			}

				this.chart.setOption(option)
			} catch (error) {
				console.error('更新饼图失败:', error)
			}
		}
	},

	beforeDestroy() {
		if (this.chart) {
			try {
				this.chart.dispose()
			} catch (error) {
				console.warn('销毁饼图实例时出错:', error)
			}
			this.chart = null
		}
	}
}
</script>

<style scoped>
.pie-chart-container {
	background: white;
	border-radius: 12px;
	padding: 16px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	height: 300px;
	display: flex;
	flex-direction: column;
}

.chart-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16px;
}

.chart-title {
	font-size: 16px;
	font-weight: bold;
	color: #333;
}

.chart-content {
	flex: 1;
	width: 100%;
	height: 100%;
}
</style>
