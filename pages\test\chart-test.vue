<template>
	<view class="chart-test-page">
		<view class="page-header">
			<view class="title">图表测试页面</view>
		</view>
		
		<!-- 测试数据显示 -->
		<view class="test-info">
			<view class="info-item">
				<text>总订单数: {{ statistics.totalOrders }}</text>
			</view>
			<view class="info-item">
				<text>已完成: {{ statistics.completedOrders }}</text>
			</view>
			<view class="info-item">
				<text>进行中: {{ statistics.processingOrders }}</text>
			</view>
			<view class="info-item">
				<text>逾期: {{ statistics.overdueOrders }}</text>
			</view>
		</view>

		<!-- 图表区域 -->
		<view class="chart-section">
			<view class="chart-container">
				<view class="trend-chart">
					<OrderTrendChart :statistics="statistics" />
				</view>
				<view class="pie-chart">
					<OrderStatusPieChart :statistics="statistics" />
				</view>
			</view>
		</view>
		
		<!-- 测试按钮 -->
		<view class="test-buttons">
			<uv-button @click="updateTestData" type="primary">更新测试数据</uv-button>
			<uv-button @click="testTimeRange" type="success" style="margin-left: 10px;">测试时间切换</uv-button>
		</view>

		<!-- 调试信息 -->
		<view class="debug-info">
			<view class="debug-title">调试信息:</view>
			<view class="debug-item">当前时间: {{ currentTime }}</view>
			<view class="debug-item">更新次数: {{ updateCount }}</view>
		</view>
	</view>
</template>

<script>
import OrderTrendChart from '../biz/scm/sale/orderprocess/components/OrderTrendChart.vue'
import OrderStatusPieChart from '../biz/scm/sale/orderprocess/components/OrderStatusPieChart.vue'

export default {
	name: 'ChartTest',
	components: {
		OrderTrendChart,
		OrderStatusPieChart
	},
	data() {
		return {
			statistics: {
				totalOrders: 75,
				completedOrders: 30,
				processingOrders: 20,
				overdueOrders: 10,
				totalGrowth: 5.2,
				completedGrowth: 8.1,
				processingGrowth: -2.3,
				overdueGrowth: 1.5
			},
			currentTime: '',
			updateCount: 0
		}
	},
	mounted() {
		this.updateCurrentTime()
		setInterval(this.updateCurrentTime, 1000)
	},
	methods: {
		updateCurrentTime() {
			this.currentTime = new Date().toLocaleTimeString()
		},

		updateTestData() {
			this.updateCount++

			// 随机更新测试数据
			this.statistics = {
				totalOrders: Math.floor(Math.random() * 100) + 50,
				completedOrders: Math.floor(Math.random() * 40) + 20,
				processingOrders: Math.floor(Math.random() * 30) + 10,
				overdueOrders: Math.floor(Math.random() * 15) + 5,
				totalGrowth: (Math.random() * 20 - 10).toFixed(1),
				completedGrowth: (Math.random() * 20 - 10).toFixed(1),
				processingGrowth: (Math.random() * 20 - 10).toFixed(1),
				overdueGrowth: (Math.random() * 20 - 10).toFixed(1)
			}

			uni.showToast({
				title: '数据已更新',
				icon: 'success'
			})
		},

		testTimeRange() {
			// 模拟快速切换时间范围来测试防抖功能
			uni.showToast({
				title: '测试时间范围切换',
				icon: 'none'
			})
		}
	}
}
</script>

<style scoped>
.chart-test-page {
	padding: 16px;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.page-header {
	text-align: center;
	margin-bottom: 20px;
}

.title {
	font-size: 20px;
	font-weight: bold;
	color: #333;
}

.test-info {
	background: white;
	border-radius: 8px;
	padding: 16px;
	margin-bottom: 16px;
}

.info-item {
	padding: 8px 0;
	border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
	border-bottom: none;
}

/* 图表区域样式 */
.chart-section {
	margin-bottom: 20px;
}

.chart-container {
	display: flex;
	gap: 16px;
	height: 300px;
}

.trend-chart {
	flex: 2;
}

.pie-chart {
	flex: 1;
}

/* 响应式设计 - 小屏幕下图表垂直排列 */
@media (max-width: 768px) {
	.chart-container {
		flex-direction: column;
		height: auto;
		gap: 16px;
	}
	
	.trend-chart,
	.pie-chart {
		flex: none;
		height: 300px;
	}
}

.test-buttons {
	text-align: center;
	margin-top: 20px;
}

.debug-info {
	background: white;
	border-radius: 8px;
	padding: 16px;
	margin-top: 16px;
}

.debug-title {
	font-weight: bold;
	margin-bottom: 8px;
	color: #333;
}

.debug-item {
	padding: 4px 0;
	color: #666;
	font-size: 14px;
}
</style>
