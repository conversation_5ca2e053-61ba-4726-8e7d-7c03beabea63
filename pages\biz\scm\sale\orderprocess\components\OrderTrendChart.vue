<template>
	<view class="trend-chart-container">
		<view class="chart-header">
			<view class="chart-title">订单趋势</view>
			<view class="time-range-selector">
				<uv-button 
					v-for="range in timeRanges" 
					:key="range.value"
					:type="timeRange === range.value ? 'primary' : 'default'"
					size="small"
					@click="handleTimeRangeChange(range.value)"
				>
					{{ range.label }}
				</uv-button>
			</view>
		</view>
		<view class="chart-content">
			<l-echart ref="chartRef" @finished="initChart"></l-echart>
		</view>
	</view>
</template>

<script>
// 引入echarts - 根据uni_modules的lime-echart文档，项目使用Vue2
import * as echarts from '@/uni_modules/lime-echart/static/echarts.min'

export default {
	name: 'OrderTrendChart',
	props: {
		statistics: {
			type: Object,
			default: () => ({})
		}
	},
	data() {
		return {
			timeRange: 'week',
			timeRanges: [
				{ label: '周', value: 'week' },
				{ label: '月', value: 'month' },
				{ label: '年', value: 'year' }
			],
			updateTimer: null
		}
	},

	// 将chart实例放在组件实例上，避免Vue响应式包装
	created() {
		this.chart = null
	},
	watch: {
		statistics: {
			handler(newVal, oldVal) {
				// 只有在数据真正变化时才更新
				if (newVal && this.chart) {
					this.$nextTick(() => {
						this.updateChart()
					})
				}
			},
			deep: true
		},
		timeRange: {
			handler(newVal, oldVal) {
				// 避免重复更新
				if (newVal !== oldVal && this.chart) {
					this.$nextTick(() => {
						this.updateChart()
					})
				}
			}
		}
	},
	methods: {
		// 获取履约趋势数据
		getTrendData() {
			const sampleData = {
				week: [
					{ date: '周一', newCount: 18, completedCount: 12 },
					{ date: '周二', newCount: 25, completedCount: 18 },
					{ date: '周三', newCount: 22, completedCount: 20 },
					{ date: '周四', newCount: 30, completedCount: 25 },
					{ date: '周五', newCount: 35, completedCount: 30 },
					{ date: '周六', newCount: 28, completedCount: 22 },
					{ date: '周日', newCount: 20, completedCount: 15 }
				],
				month: [
					{ date: '第1周', newCount: 110, completedCount: 85 },
					{ date: '第2周', newCount: 135, completedCount: 115 },
					{ date: '第3周', newCount: 125, completedCount: 105 },
					{ date: '第4周', newCount: 145, completedCount: 125 }
				],
				year: [
					{ date: '第一季度', newCount: 450, completedCount: 380 },
					{ date: '第二季度', newCount: 520, completedCount: 465 },
					{ date: '第三季度', newCount: 485, completedCount: 420 },
					{ date: '第四季度', newCount: 580, completedCount: 510 }
				]
			}
			return sampleData[this.timeRange] || sampleData.week
		},

		// 初始化图表
		initChart() {
			if (!this.$refs.chartRef) return

			try {
				// 根据lime-echart文档，Vue2项目中的初始化方式
				this.$refs.chartRef.init(echarts, (chart) => {
					// 使用Object.defineProperty避免Vue响应式包装
					Object.defineProperty(this, 'chart', {
						value: chart,
						writable: true,
						enumerable: false,
						configurable: true
					})

					// 延迟更新图表，确保DOM完全渲染
					this.$nextTick(() => {
						this.updateChart()
					})
				})
			} catch (error) {
				console.error('初始化图表失败:', error)
			}
		},

		// 更新图表
		updateChart() {
			if (!this.chart || !this.$refs.chartRef) return

			try {
				const trendData = this.getTrendData()
				const dates = trendData.map(item => item.date)
				const newOrders = trendData.map(item => item.newCount)
				const completedOrders = trendData.map(item => item.completedCount)

			const option = {
				tooltip: {
					trigger: 'axis',
					axisPointer: {
						type: 'cross',
						label: {
							backgroundColor: '#6a7985'
						}
					}
				},
				legend: {
					data: ['新增履约', '完成履约'],
					top: 10,
					left: 'center'
				},
				grid: {
					left: '3%',
					right: '4%',
					bottom: '15%',
					top: '20%',
					containLabel: true
				},
				xAxis: [{
					type: 'category',
					boundaryGap: false,
					data: dates,
					axisLabel: {
						rotate: 30,
						fontSize: 10
					}
				}],
				yAxis: [{
					type: 'value',
					axisLine: {
						show: true
					},
					splitLine: {
						show: true
					}
				}],
				series: [
					{
						name: '新增履约',
						type: 'line',
						smooth: true,
						lineStyle: {
							width: 3,
							color: '#3b82f6'
						},
						showSymbol: false,
						areaStyle: {
							color: {
								type: 'linear',
								x: 0,
								y: 0,
								x2: 0,
								y2: 1,
								colorStops: [
									{ offset: 0, color: 'rgba(59, 130, 246, 0.8)' },
									{ offset: 1, color: 'rgba(59, 130, 246, 0.1)' }
								]
							}
						},
						data: newOrders
					},
					{
						name: '完成履约',
						type: 'line',
						smooth: true,
						lineStyle: {
							width: 3,
							color: '#10b981'
						},
						showSymbol: false,
						areaStyle: {
							color: {
								type: 'linear',
								x: 0,
								y: 0,
								x2: 0,
								y2: 1,
								colorStops: [
									{ offset: 0, color: 'rgba(16, 185, 129, 0.8)' },
									{ offset: 1, color: 'rgba(16, 185, 129, 0.1)' }
								]
							}
						},
						data: completedOrders
					}
				]
			}

				this.chart.setOption(option)
			} catch (error) {
				console.error('更新图表失败:', error)
			}
		},

		// 时间范围切换
		handleTimeRangeChange(range) {
			if (this.timeRange === range) return

			this.timeRange = range

			// 清除之前的定时器
			if (this.updateTimer) {
				clearTimeout(this.updateTimer)
			}

			// 防抖更新图表
			this.updateTimer = setTimeout(() => {
				this.$nextTick(() => {
					this.updateChart()
				})
			}, 100)
		}
	},

	beforeDestroy() {
		// 清理定时器
		if (this.updateTimer) {
			clearTimeout(this.updateTimer)
			this.updateTimer = null
		}

		// 销毁图表实例
		if (this.chart) {
			try {
				this.chart.dispose()
			} catch (error) {
				console.warn('销毁图表实例时出错:', error)
			}
			this.chart = null
		}
	}
}
</script>

<style scoped>
.trend-chart-container {
	background: white;
	border-radius: 12px;
	padding: 16px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	height: 300px;
	display: flex;
	flex-direction: column;
}

.chart-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16px;
}

.chart-title {
	font-size: 16px;
	font-weight: bold;
	color: #333;
}

.time-range-selector {
	display: flex;
	gap: 8px;
}

.chart-content {
	flex: 1;
	width: 100%;
	height: 100%;
}
</style>
