<template>
	<view class="work-order-page">
		<!-- 搜索栏 -->
		<view class="search-section">
			<uv-search
				v-model="searchKeyword"
				placeholder="请输入生产单号或产品名称"
				shape="round"
				bgColor="#f5f5f5"
				clearable
				@search="handleSearch"
				@custom="handleSearch"
				@clear="handleClearSearch"
			></uv-search>
		</view>

		<!-- 筛选标签 -->
		<view class="filter-section">
			<uv-tabs
				:list="filterTabs"
				@click="handleTabChange"
				:current="currentTabIndex"
				keyName="name"
				activeColor="#007AFF"
				inactiveColor="#666"
				lineColor="#007AFF"
			></uv-tabs>
		</view>

		<!-- 列表容器 -->
		<scroll-view
			scroll-y="true"
			class="list-container"
			@scrolltolower="loadMoreData"
			refresher-enabled
			:refresher-triggered="isRefreshing"
			@refresherrefresh="onPullDownRefresh"
			enable-flex="true"
		>
			<!-- 工作订单列表 -->
			<view class="work-order-list" v-if="workOrderList.length > 0">
				<work-order-list-item
					v-for="(item, index) in workOrderList"
					:key="item.id || index"
					:item="item"
					@click="handleItemClick"
					@report="handleReportClick"
					@edit="handleEditClick"
					@delete="handleDeleteClick"
					@start="handleStartClick"
					@complete="handleCompleteClick"
				/>
			</view>

			<!-- 空数据状态 -->
			<view v-else-if="!isLoading && !isRefreshing" class="empty-state">
				<view class="empty-icon">📋</view>
				<text class="empty-text">暂无生产任务数据</text>
				<text class="empty-tip">请尝试调整筛选条件或刷新页面</text>
			</view>

			<!-- 加载更多状态 -->
			<view v-if="isLoading && workOrderList.length > 0" class="loading-more">
				<uni-icons type="spinner-cycle" size="20" color="#007AFF"></uni-icons>
				<text class="loading-text">加载中...</text>
			</view>

			<!-- 没有更多数据 -->
			<view v-if="loadMoreStatus === 'noMore' && workOrderList.length > 0" class="no-more">
				<text class="no-more-text">没有更多数据了</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
import { getWorkOrderPageApi } from '../../../../../api/scm/mfg/workorder';
import WorkOrderListItem from './components/workOrderListItem.vue';
import { getBatchDictOptions, DICT_TYPE } from '../../../../../utils/dict';

export default {
	name: 'WorkOrder',
	components: {
		WorkOrderListItem
	},
	data() {
		return {
			// 搜索关键词
			searchKeyword: '',

			// 工作订单列表
			workOrderList: [],

			// 分页参数
			pageNo: 1,
			pageSize: 10,
			totalPages: 0,

			// 加载状态
			isLoading: false,
			isRefreshing: false,
			loadMoreStatus: 'more', // more, loading, noMore

			// 筛选标签
			filterTabs: [
				{ name: '全部', value: 'all' },
				{ name: '今日', value: 'today' },
				{ name: '明日', value: 'tomorrow' },
				{ name: '未完成', value: 'unfinished' }
			],
			currentTabIndex: 0,
			currentFilter: 'all',

			// 请求参数
			requestParams: {
				pageNo: 1,
				pageSize: 10,
				status: [],
				scheduleStartDate: [],
				productName: '',
				workNo: ''
			}
		}
	},

	onLoad() {
		this.initData();
	},

	methods: {
		// 初始化数据
		initData() {
			this.resetParams();
			this.getWorkOrderList(true);
		},

		// 重置参数
		resetParams() {
			this.pageNo = 1;
			this.workOrderList = [];
			this.loadMoreStatus = 'more';
			this.requestParams = {
				pageNo: 1,
				pageSize: this.pageSize,
				status: [],
				scheduleStartDate: [],
				productName: '',
				workNo: ''
			};
		},

		// 获取工作订单列表
		async getWorkOrderList(isRefresh = false) {
			if (this.isLoading) return;

			this.isLoading = true;
			this.loadMoreStatus = 'loading';

			try {
				// 设置分页参数
				this.requestParams.pageNo = isRefresh ? 1 : this.pageNo;
				this.requestParams.pageSize = this.pageSize;

				// 设置搜索条件
				if (this.searchKeyword.trim()) {
					// 判断是否为生产单号（通常包含特定格式）
					if (this.searchKeyword.includes('-') || /^[A-Z]/.test(this.searchKeyword)) {
						this.requestParams.workNo = this.searchKeyword.trim();
						this.requestParams.productName = '';
					} else {
						this.requestParams.productName = this.searchKeyword.trim();
						this.requestParams.workNo = '';
					}
				} else {
					this.requestParams.productName = '';
					this.requestParams.workNo = '';
				}

				// 设置筛选条件
				this.setFilterParams();

				const response = await getWorkOrderPageApi(this.requestParams);

				if (response && response.code === 0 && response.data) {
					const newList = response.data.list || [];

					if (isRefresh) {
						this.workOrderList = newList;
						this.pageNo = 1;
					} else {
						this.workOrderList = [...this.workOrderList, ...newList];
					}

					// 更新分页信息
					this.totalPages = Math.ceil((response.data.total || 0) / this.pageSize);

					// 判断是否还有更多数据
					if (newList.length < this.pageSize || this.pageNo >= this.totalPages) {
						this.loadMoreStatus = 'noMore';
					} else {
						this.loadMoreStatus = 'more';
						this.pageNo++;
					}
				} else {
					uni.showToast({
						title: response.msg || '获取数据失败',
						icon: 'none'
					});
					this.loadMoreStatus = 'noMore';
				}
			} catch (error) {
				console.error('获取工作订单列表失败:', error);
				uni.showToast({
					title: '网络错误，请重试',
					icon: 'none'
				});
				this.loadMoreStatus = 'noMore';
			} finally {
				this.isLoading = false;
				if (this.isRefreshing) {
					this.isRefreshing = false;
				}
			}
		},

		// 设置筛选参数
		setFilterParams() {
			const today = new Date();
			const todayStr = today.toISOString().split('T')[0];

			const tomorrow = new Date(today);
			tomorrow.setDate(tomorrow.getDate() + 1);
			const tomorrowStr = tomorrow.toISOString().split('T')[0];

			// 重置筛选条件
			this.requestParams.status = [];
			this.requestParams.scheduleStartDate = [];

			switch (this.currentFilter) {
				case 'today':
					this.requestParams.scheduleStartDate = [`${todayStr} 00:00:00`, `${todayStr} 23:59:59`];
					break;
				case 'tomorrow':
					this.requestParams.scheduleStartDate = [`${tomorrowStr} 00:00:00`, `${tomorrowStr} 23:59:59`];
					break;
				case 'unfinished':
					// 未完成状态：0, 1, 2, 3
					this.requestParams.status = ['0', '1', '2', '3'];
					break;
				case 'all':
				default:
					// 不设置任何过滤条件
					break;
			}
		},

		// 处理搜索
		handleSearch() {
			this.resetParams();
			this.getWorkOrderList(true);
		},

		// 清除搜索
		handleClearSearch() {
			this.searchKeyword = '';
			this.resetParams();
			this.getWorkOrderList(true);
		},

		// 处理标签切换
		handleTabChange(item, index) {
			this.currentTabIndex = index;
			this.currentFilter = item.value;
			this.resetParams();
			this.getWorkOrderList(true);
		},

		// 下拉刷新
		onPullDownRefresh() {
			this.isRefreshing = true;
			this.resetParams();
			this.getWorkOrderList(true);
		},

		// 加载更多
		loadMoreData() {
			if (this.loadMoreStatus === 'more' && !this.isLoading) {
				this.getWorkOrderList(false);
			}
		},

		// 处理列表项点击
		handleItemClick(item) {
			// 跳转到工单详情页面
			uni.navigateTo({
				url: `/pages/biz/scm/mfg/workorder/detail?id=${item.id}&workNo=${encodeURIComponent(item.workNo || '')}`
			});
		},

		// 处理报工操作
		handleReportClick(item) {
			// 验证必要的数据
			if (!item || !item.id) {
				console.error('工单数据不完整，无法进行报工操作')
				uni.showToast({
					title: '工单数据不完整',
					icon: 'error'
				})
				return
			}

			// 检查审核状态，只有审核通过的工单才能报工
			if (item.approveStatus !== 3) {
				uni.showToast({
					title: '工单未审核通过，无法报工',
					icon: 'none'
				})
				return
			}

			uni.navigateTo({
				url:'/pages/biz/scm/mfg/reportorder/reportEdit',
				success:(res) => {

					res.eventChannel.emit('acceptDataFormOpener',{
						workId: item.id,
					})
				},
				fail:(err) => {
					console.error('页面跳转失败:', err)
					uni.showToast({
						title: '页面跳转失败',
						icon: 'error'
					})
				}
			})
		},
	}
}
</script>

<style lang="scss" scoped>
/* 页面容器 */
.work-order-page {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 搜索区域 */
.search-section {
	background-color: white;
	padding: 24rpx;
	border-bottom: 1px solid #f0f0f0;
}

/* 筛选区域 */
.filter-section {
	background-color: white;
	padding: 0 24rpx;
	border-bottom: 1px solid #f0f0f0;
}

/* 列表容器 */
.list-container {
	flex: 1;
	height: calc(100vh - 200rpx); /* 减去搜索栏和筛选栏的高度 */
	padding: 24rpx;
}

/* 工作订单列表 */
.work-order-list {
	padding-bottom: 40rpx;
}

/* 空数据状态 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 40rpx;

	.empty-icon {
		font-size: 120rpx;
		margin-bottom: 32rpx;
		opacity: 0.6;
	}

	.empty-text {
		font-size: 32rpx;
		color: #666;
		margin-bottom: 16rpx;
		font-weight: 500;
	}

	.empty-tip {
		font-size: 26rpx;
		color: #999;
		text-align: center;
		line-height: 1.5;
	}
}

/* 加载更多状态 */
.loading-more {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;

	.loading-text {
		font-size: 26rpx;
		color: #666;
		margin-left: 16rpx;
	}
}

/* 没有更多数据 */
.no-more {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;

	.no-more-text {
		font-size: 26rpx;
		color: #999;
	}
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
	.list-container {
		padding: 16rpx;
	}

	.search-section {
		padding: 16rpx;
	}

	.filter-section {
		padding: 0 16rpx;
	}
}
</style>
